import 'package:flutter_test/flutter_test.dart';
import 'package:powersights/services/huggingface_api_service.dart';
import 'package:powersights/config/api_config.dart';
import 'package:powersights/models/item_detection_result.dart';

void main() {
  group('Hugging Face API Service Tests', () {
    late HuggingFaceApiService apiService;

    setUp(() {
      apiService = HuggingFaceApiService();
    });

    test('should create VisionAnalysisResult with mock data', () {
      final mockResult = VisionAnalysisResult.mock();
      
      expect(mockResult.description, isNotEmpty);
      expect(mockResult.confidence, greaterThan(0.0));
      expect(mockResult.confidence, lessThanOrEqualTo(1.0));
      expect(mockResult.detectedObjects, isNotEmpty);
      expect(mockResult.colors, isNotEmpty);
      expect(mockResult.tags, isNotEmpty);
    });

    test('should have valid API configuration', () {
      expect(ApiConfig.huggingFaceRouterUrl, isNotEmpty);
      expect(ApiConfig.visionLanguageModels, isNotEmpty);
      expect(ApiConfig.legacyInferenceUrl, isNotEmpty);
      expect(ApiConfig.apiTimeout.inSeconds, greaterThan(0));
      expect(ApiConfig.defaultPrompts, isNotEmpty);
      expect(ApiConfig.defaultModelParams, isNotEmpty);
    });

    test('should have valid default prompts for different analysis types', () {
      expect(ApiConfig.defaultPrompts['general'], isNotEmpty);
      expect(ApiConfig.defaultPrompts['objects'], isNotEmpty);
      expect(ApiConfig.defaultPrompts['colors'], isNotEmpty);
      expect(ApiConfig.defaultPrompts['composition'], isNotEmpty);
      expect(ApiConfig.defaultPrompts['text'], isNotEmpty);
      expect(ApiConfig.defaultPrompts['items'], isNotEmpty);
    });

    test('should create ItemDetectionResult with mock data', () {
      final mockResult = ItemDetectionResult.mock();

      expect(mockResult.items, isNotEmpty);
      expect(mockResult.items.length, greaterThan(0));
      expect(mockResult.isMockData, isTrue);

      // Check first item has required fields
      final firstItem = mockResult.items.first;
      expect(firstItem.name, isNotEmpty);
      expect(firstItem.confidence, greaterThan(0.0));
      expect(firstItem.confidence, lessThanOrEqualTo(1.0));
    });

    test('should parse JSON item detection response', () {
      const jsonResponse = '''
      {
        "items": [
          {
            "name": "Test Product",
            "brand": "Test Brand",
            "size": "500ML",
            "productType": "Bottle",
            "price": "\$3.99",
            "description": "Test Description",
            "confidence": 0.9
          }
        ]
      }
      ''';

      final result = ItemDetectionResult.fromJson(jsonResponse);

      expect(result.items.length, equals(1));
      expect(result.items.first.name, equals('Test Product'));
      expect(result.items.first.brand, equals('Test Brand'));
      expect(result.items.first.size, equals('500ML'));
      expect(result.items.first.productType, equals('Bottle'));
      expect(result.items.first.price, equals('\$3.99'));
      expect(result.items.first.confidence, equals(0.9));
    });

    test('should parse text item detection response', () {
      const textResponse = '''
      Here are the items I can see:
      - Budweiser Premium Can 330ML - \$4.99
      - Coca-Cola Bottle 500ML - \$2.49
      - Lay's Chips Bag 150g
      ''';

      final result = ItemDetectionResult.fromText(textResponse);

      expect(result.items.length, greaterThan(0));
      expect(result.items.any((item) => item.name.contains('Budweiser')), isTrue);
    });

    test('should extract product types from text', () {
      const textResponse = '''
      - Beer Can 330ML
      - Soda Bottle 500ML
      - Chips Bag 150g
      - Cereal Box 400g
      ''';

      final result = ItemDetectionResult.fromText(textResponse);

      expect(result.items.length, equals(4));
      expect(result.items.any((item) => item.productType == 'Can'), isTrue);
      expect(result.items.any((item) => item.productType == 'Bottle'), isTrue);
      expect(result.items.any((item) => item.productType == 'Bag'), isTrue);
      expect(result.items.any((item) => item.productType == 'Box'), isTrue);
    });

    test('should have reasonable model parameters', () {
      final params = ApiConfig.defaultModelParams;
      
      expect(params['temperature'], isA<double>());
      expect(params['max_tokens'], isA<int>());
      expect(params['top_p'], isA<double>());
      
      expect(params['temperature'], greaterThan(0.0));
      expect(params['temperature'], lessThanOrEqualTo(2.0));
      expect(params['max_tokens'], greaterThan(0));
      expect(params['top_p'], greaterThan(0.0));
      expect(params['top_p'], lessThanOrEqualTo(1.0));
    });

    test('should handle ApiException correctly', () {
      const exception = ApiException('Test error message');
      
      expect(exception.message, equals('Test error message'));
      expect(exception.toString(), contains('ApiException'));
      expect(exception.toString(), contains('Test error message'));
    });

    test('VisionAnalysisResult should have all required fields', () {
      const result = VisionAnalysisResult(
        description: 'Test description',
        confidence: 0.85,
        detectedObjects: ['object1', 'object2'],
        colors: ['red', 'blue'],
        tags: ['tag1', 'tag2'],
        rawResponse: {'test': 'data'},
      );

      expect(result.description, equals('Test description'));
      expect(result.confidence, equals(0.85));
      expect(result.detectedObjects, hasLength(2));
      expect(result.colors, hasLength(2));
      expect(result.tags, hasLength(2));
      expect(result.rawResponse, isNotEmpty);
    });

    // Note: This test would require actual network access and a valid image
    // Uncomment and modify for integration testing
    /*
    test('should analyze image with real API call', () async {
      // This test requires a real image file and network access
      // Use only for integration testing
      
      final testImageBytes = Uint8List.fromList([
        // Add some test image bytes here
      ]);

      try {
        final result = await apiService.analyzeImage(
          imageFile: testImageBytes,
          prompt: 'Test analysis',
        );

        expect(result, isA<VisionAnalysisResult>());
        expect(result.description, isNotEmpty);
        expect(result.confidence, greaterThan(0.0));
      } catch (e) {
        // Expected if API is not available or image is invalid
        expect(e, isA<ApiException>());
      }
    }, timeout: const Timeout(Duration(minutes: 2)));
    */
  });
}
