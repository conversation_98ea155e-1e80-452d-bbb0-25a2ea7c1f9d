import 'dart:convert';

/// Represents a detected item in an image with its details
class DetectedItem {
  final String name;
  final String? price;
  final String? brand;
  final String? size;
  final String? description;
  final double confidence;

  const DetectedItem({
    required this.name,
    this.price,
    this.brand,
    this.size,
    this.description,
    this.confidence = 0.8,
  });

  factory DetectedItem.fromJson(Map<String, dynamic> json) {
    return DetectedItem(
      name: json['name'] as String? ?? 'Unknown Item',
      price: json['price'] as String?,
      brand: json['brand'] as String?,
      size: json['size'] as String?,
      description: json['description'] as String?,
      confidence: (json['confidence'] as num?)?.toDouble() ?? 0.8,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'price': price,
      'brand': brand,
      'size': size,
      'description': description,
      'confidence': confidence,
    };
  }

  @override
  String toString() {
    final parts = <String>[name];
    if (brand != null) parts.add(brand!);
    if (size != null) parts.add(size!);
    if (price != null) parts.add('- $price');
    return parts.join(' ');
  }
}

/// Result class for item detection analysis
class ItemDetectionResult {
  final List<DetectedItem> items;
  final String rawResponse;
  final bool isMockData;
  final String? errorMessage;

  const ItemDetectionResult({
    required this.items,
    required this.rawResponse,
    this.isMockData = false,
    this.errorMessage,
  });

  factory ItemDetectionResult.fromJson(String jsonString) {
    try {
      final Map<String, dynamic> json = jsonDecode(jsonString);
      
      final List<DetectedItem> items = [];
      if (json['items'] is List) {
        for (final itemJson in json['items']) {
          if (itemJson is Map<String, dynamic>) {
            items.add(DetectedItem.fromJson(itemJson));
          }
        }
      }

      return ItemDetectionResult(
        items: items,
        rawResponse: jsonString,
      );
    } catch (e) {
      return ItemDetectionResult(
        items: [],
        rawResponse: jsonString,
        errorMessage: 'Failed to parse JSON: $e',
      );
    }
  }

  /// Creates a mock result for testing purposes
  factory ItemDetectionResult.mock() {
    return ItemDetectionResult(
      items: [
        const DetectedItem(
          name: 'Budweiser Premium',
          brand: 'Budweiser',
          size: '330ML',
          price: '\$4.99',
          description: '1 Pack Premium Beer',
          confidence: 0.9,
        ),
        const DetectedItem(
          name: 'Coca-Cola Classic',
          brand: 'Coca-Cola',
          size: '500ML',
          price: '\$2.49',
          description: 'Classic Soft Drink',
          confidence: 0.85,
        ),
        const DetectedItem(
          name: 'Lay\'s Potato Chips',
          brand: 'Lay\'s',
          size: '150g',
          price: '\$3.29',
          description: 'Original Flavor',
          confidence: 0.8,
        ),
      ],
      rawResponse: '{"items": [{"name": "Mock Data", "price": "Demo"}]}',
      isMockData: true,
    );
  }

  /// Attempts to extract items from free-form text response
  factory ItemDetectionResult.fromText(String text) {
    final items = <DetectedItem>[];
    
    // Try to find JSON in the text first
    final jsonMatch = RegExp(r'\{.*\}', dotAll: true).firstMatch(text);
    if (jsonMatch != null) {
      try {
        return ItemDetectionResult.fromJson(jsonMatch.group(0)!);
      } catch (e) {
        // Fall through to text parsing
      }
    }

    // Parse line by line for item-like patterns
    final lines = text.split('\n');
    for (final line in lines) {
      final trimmed = line.trim();
      if (trimmed.isEmpty) continue;

      // Look for patterns like "Item Name - Price" or "Brand Item Size - Price"
      final itemMatch = RegExp(r'^[•\-\*]?\s*(.+?)(?:\s*-\s*(.+?))?$').firstMatch(trimmed);
      if (itemMatch != null) {
        final nameAndDetails = itemMatch.group(1)?.trim() ?? '';
        final priceInfo = itemMatch.group(2)?.trim();

        if (nameAndDetails.isNotEmpty) {
          // Try to extract brand, name, and size from the name part
          String name = nameAndDetails;
          String? brand;
          String? size;

          // Look for size patterns (e.g., "330ML", "500g", "1L")
          final sizeMatch = RegExp(r'\b(\d+(?:\.\d+)?(?:ML|L|g|kg|oz|pack))\b', caseSensitive: false).firstMatch(name);
          if (sizeMatch != null) {
            size = sizeMatch.group(1);
            name = name.replaceFirst(sizeMatch.group(0)!, '').trim();
          }

          // Look for common brand patterns
          final brandPatterns = ['Budweiser', 'Coca-Cola', 'Pepsi', 'Lay\'s', 'Pringles', 'Heineken'];
          for (final brandPattern in brandPatterns) {
            if (name.toLowerCase().contains(brandPattern.toLowerCase())) {
              brand = brandPattern;
              break;
            }
          }

          items.add(DetectedItem(
            name: name,
            brand: brand,
            size: size,
            price: priceInfo,
            confidence: 0.7,
          ));
        }
      }
    }

    return ItemDetectionResult(
      items: items,
      rawResponse: text,
    );
  }

  String toJsonString() {
    return jsonEncode({
      'items': items.map((item) => item.toJson()).toList(),
    });
  }
}
