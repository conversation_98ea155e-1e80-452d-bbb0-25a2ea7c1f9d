import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart'; // For kIsWeb
import 'package:google_fonts/google_fonts.dart';

import '../widgets/custom_widgets.dart';
import '../theme/app_colors.dart';
import '../services/huggingface_api_service.dart';
import '../models/item_detection_result.dart';
import 'photo_input_screen.dart';

/// Enhanced screen that displays the selected image along with analysis results
/// featuring beautiful animations and modern UI design.
class PhotoResultScreen extends StatefulWidget {
  final dynamic imageFile; // File or bytes

  const PhotoResultScreen({super.key, required this.imageFile});

  @override
  State<PhotoResultScreen> createState() => _PhotoResultScreenState();
}

class _PhotoResultScreenState extends State<PhotoResultScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late AnimationController _imageController;
  late AnimationController _resultsController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _imageScaleAnimation;
  late Animation<double> _resultsOpacityAnimation;
  late Animation<double> _resultsScaleAnimation;

  bool _isAnalyzing = true;
  VisionAnalysisResult? _analysisResult;
  ItemDetectionResult? _itemDetectionResult;
  String? _errorMessage;
  final HuggingFaceApiService _apiService = HuggingFaceApiService();

  @override
  void initState() {
    super.initState();

    // Initialize animations
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _imageController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _resultsController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.5), end: Offset.zero).animate(
          CurvedAnimation(parent: _slideController, curve: Curves.elasticOut),
        );

    _imageScaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _imageController, curve: Curves.elasticOut),
    );

    _resultsOpacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _resultsController, curve: Curves.easeIn),
    );

    _resultsScaleAnimation = Tween<double>(begin: 0.9, end: 1.0).animate(
      CurvedAnimation(parent: _resultsController, curve: Curves.elasticOut),
    );

    // Start animations with staggered timing
    _fadeController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) {
        _imageController.forward();
      }
    });

    // Start real image analysis
    _analyzeImage();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _imageController.dispose();
    _resultsController.dispose();
    super.dispose();
  }

  /// Analyzes the image using Hugging Face InternVL API for item detection
  Future<void> _analyzeImage() async {
    try {
      // Call the API service for item detection
      final itemResult = await _apiService.analyzeImageForItems(
        imageFile: widget.imageFile,
      );

      if (mounted) {
        setState(() {
          _itemDetectionResult = itemResult;
          _isAnalyzing = false;
          _errorMessage = null;
        });
        _resultsController.forward();
        _slideController.forward();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
          _isAnalyzing = false;
        });
        _resultsController.forward();
        _slideController.forward();
      }
    }
  }



  Widget _buildAnalysisResults() {
    // Show error state if there's an error
    if (_errorMessage != null) {
      return _buildErrorState();
    }

    // Show results if analysis is complete
    if (_itemDetectionResult != null) {
      return _buildItemDetectionResults();
    }

    // Fallback to loading state
    return _buildAnalyzingIndicator();
  }

  Widget _buildErrorState() {
    return AnimatedBuilder(
      animation: _resultsController,
      builder: (context, child) {
        return Transform.scale(
          scale: _resultsScaleAnimation.value,
          child: Opacity(
            opacity: _resultsOpacityAnimation.value,
            child: SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: Column(
                children: [
                  PowerCard(
                    elevated: true,
                    child: Column(
                      children: [
                        // Error icon
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: AppColors.red.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Icon(
                            Icons.error_outline_rounded,
                            size: 48,
                            color: AppColors.red,
                          ),
                        ),

                        const SizedBox(height: 16),

                        Text(
                          'Analysis Failed',
                          style: GoogleFonts.inter(
                            fontSize: 20,
                            fontWeight: FontWeight.w700,
                            color: AppColors.primaryText,
                            letterSpacing: 0.1,
                          ),
                        ),

                        const SizedBox(height: 12),

                        Text(
                          _errorMessage ?? 'Unknown error occurred',
                          style: GoogleFonts.inter(
                            fontSize: 14,
                            color: AppColors.secondaryText,
                            height: 1.4,
                          ),
                          textAlign: TextAlign.center,
                        ),

                        const SizedBox(height: 24),

                        PowerButton(
                          text: 'Try Again',
                          icon: Icons.refresh_rounded,
                          onPressed: () {
                            setState(() {
                              _isAnalyzing = true;
                              _errorMessage = null;
                              _analysisResult = null;
                              _itemDetectionResult = null;
                            });
                            _analyzeImage();
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSuccessResults() {
    final result = _analysisResult!;

    return AnimatedBuilder(
      animation: _resultsController,
      builder: (context, child) {
        return Transform.scale(
          scale: _resultsScaleAnimation.value,
          child: Opacity(
            opacity: _resultsOpacityAnimation.value,
            child: SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: Column(
                children: [
                  // Main results card
                  PowerCard(
                    elevated: true,
                    child: Column(
                      children: [
                        // Analytics icon with glow effect
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: AppColors.gold.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: AppColors.gold.withOpacity(0.3),
                                blurRadius: 15,
                                spreadRadius: 2,
                              ),
                            ],
                          ),
                          child: const Icon(
                            Icons.analytics_outlined,
                            color: AppColors.gold,
                            size: 40,
                          ),
                        ),

                        const SizedBox(height: 20),

                        Text(
                          'Analysis Complete',
                          style: GoogleFonts.inter(
                            fontSize: 24,
                            fontWeight: FontWeight.w700,
                            color: AppColors.primaryText,
                            letterSpacing: -0.5,
                          ),
                        ),

                        const SizedBox(height: 12),

                        Text(
                          'Your image has been successfully analyzed. Here are the key insights:',
                          style: GoogleFonts.inter(
                            fontSize: 15,
                            color: AppColors.secondaryText,
                            height: 1.4,
                            letterSpacing: 0.1,
                          ),
                          textAlign: TextAlign.center,
                        ),

                        const SizedBox(height: 24),

                        // Image description
                        Container(
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            color: AppColors.surface.withOpacity(0.5),
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: AppColors.gold.withOpacity(0.2),
                              width: 1,
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    Icons.description_outlined,
                                    color: AppColors.gold,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    'Description',
                                    style: GoogleFonts.inter(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                      color: AppColors.primaryText,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 12),
                              Text(
                                result.description,
                                style: GoogleFonts.inter(
                                  fontSize: 14,
                                  color: AppColors.secondaryText,
                                  height: 1.5,
                                ),
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Analysis metrics
                        Container(
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            color: AppColors.surface.withOpacity(0.5),
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: AppColors.gold.withOpacity(0.2),
                              width: 1,
                            ),
                          ),
                          child: Column(
                            children: [
                              _buildResultItem(
                                'Confidence Score',
                                '${(result.confidence * 100).toStringAsFixed(1)}%',
                                Icons.verified_outlined,
                                AppColors.success,
                              ),
                              if (result.detectedObjects.isNotEmpty) ...[
                                const SizedBox(height: 16),
                                _buildResultItem(
                                  'Objects Detected',
                                  result.detectedObjects.length.toString(),
                                  Icons.visibility_outlined,
                                  AppColors.info,
                                ),
                              ],
                              if (result.colors.isNotEmpty) ...[
                                const SizedBox(height: 16),
                                _buildResultItem(
                                  'Colors Identified',
                                  result.colors.length.toString(),
                                  Icons.palette_outlined,
                                  AppColors.gold,
                                ),
                              ],
                            ],
                          ),
                        ),

                        if (result.detectedObjects.isNotEmpty) ...[
                          const SizedBox(height: 16),
                          _buildTagsSection('Objects', result.detectedObjects, Icons.visibility_outlined),
                        ],

                        if (result.colors.isNotEmpty) ...[
                          const SizedBox(height: 16),
                          _buildTagsSection('Colors', result.colors, Icons.palette_outlined),
                        ],

                        if (result.tags.isNotEmpty) ...[
                          const SizedBox(height: 16),
                          _buildTagsSection('Tags', result.tags, Icons.label_outlined),
                        ],

                        // Show mock data indicator if applicable
                        if (result.isMockData) ...[
                          const SizedBox(height: 20),
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: AppColors.gold.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: AppColors.gold.withOpacity(0.3),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.warning_amber_rounded,
                                  color: AppColors.gold,
                                  size: 20,
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Using Mock Data',
                                        style: GoogleFonts.inter(
                                          fontSize: 14,
                                          fontWeight: FontWeight.w600,
                                          color: AppColors.gold,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        'The AI API is currently unavailable. This is placeholder data for demonstration.',
                                        style: GoogleFonts.inter(
                                          fontSize: 12,
                                          color: AppColors.secondaryText,
                                          height: 1.3,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Action button
                  SlideTransition(
                    position: _slideAnimation,
                    child: PowerButton(
                      text: 'New Analysis',
                      icon: Icons.add_a_photo_rounded,
                      onPressed: _handleNewAnalysis,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildItemDetectionResults() {
    final result = _itemDetectionResult!;

    return AnimatedBuilder(
      animation: _resultsController,
      builder: (context, child) {
        return Transform.scale(
          scale: _resultsScaleAnimation.value,
          child: Opacity(
            opacity: _resultsOpacityAnimation.value,
            child: SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: Column(
                children: [
                  // Main results card
                  PowerCard(
                    elevated: true,
                    child: Column(
                      children: [
                        // Shopping cart icon with glow effect
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: AppColors.gold.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: AppColors.gold.withOpacity(0.3),
                                blurRadius: 15,
                                spreadRadius: 2,
                              ),
                            ],
                          ),
                          child: const Icon(
                            Icons.shopping_cart_outlined,
                            color: AppColors.gold,
                            size: 40,
                          ),
                        ),

                        const SizedBox(height: 20),

                        Text(
                          'Items Detected',
                          style: GoogleFonts.inter(
                            fontSize: 24,
                            fontWeight: FontWeight.w700,
                            color: AppColors.primaryText,
                            letterSpacing: -0.5,
                          ),
                        ),

                        const SizedBox(height: 12),

                        Text(
                          result.items.isNotEmpty
                            ? 'Found ${result.items.length} item${result.items.length == 1 ? '' : 's'} in your image:'
                            : 'No items were detected in this image.',
                          style: GoogleFonts.inter(
                            fontSize: 15,
                            color: AppColors.secondaryText,
                            height: 1.4,
                            letterSpacing: 0.1,
                          ),
                          textAlign: TextAlign.center,
                        ),

                        const SizedBox(height: 24),

                        // Items list
                        if (result.items.isNotEmpty) ...[
                          ...result.items.asMap().entries.map((entry) {
                            final index = entry.key;
                            final item = entry.value;
                            return Column(
                              children: [
                                if (index > 0) const SizedBox(height: 16),
                                _buildItemCard(item, index + 1),
                              ],
                            );
                          }).toList(),
                        ] else ...[
                          // No items found
                          Container(
                            padding: const EdgeInsets.all(20),
                            decoration: BoxDecoration(
                              color: AppColors.surface.withOpacity(0.5),
                              borderRadius: BorderRadius.circular(16),
                              border: Border.all(
                                color: AppColors.gold.withOpacity(0.2),
                                width: 1,
                              ),
                            ),
                            child: Column(
                              children: [
                                Icon(
                                  Icons.search_off_rounded,
                                  color: AppColors.secondaryText,
                                  size: 48,
                                ),
                                const SizedBox(height: 12),
                                Text(
                                  'No Items Found',
                                  style: GoogleFonts.inter(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: AppColors.primaryText,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'Try taking a photo with clear product labels or packaging.',
                                  style: GoogleFonts.inter(
                                    fontSize: 14,
                                    color: AppColors.secondaryText,
                                    height: 1.4,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          ),
                        ],

                        // Show mock data indicator if applicable
                        if (result.isMockData) ...[
                          const SizedBox(height: 20),
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: AppColors.gold.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: AppColors.gold.withOpacity(0.3),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.warning_amber_rounded,
                                  color: AppColors.gold,
                                  size: 20,
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Using Demo Data',
                                        style: GoogleFonts.inter(
                                          fontSize: 14,
                                          fontWeight: FontWeight.w600,
                                          color: AppColors.gold,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        'The AI API is currently unavailable. This is sample data for demonstration.',
                                        style: GoogleFonts.inter(
                                          fontSize: 12,
                                          color: AppColors.secondaryText,
                                          height: 1.3,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Action button
                  SlideTransition(
                    position: _slideAnimation,
                    child: PowerButton(
                      text: 'New Analysis',
                      icon: Icons.add_a_photo_rounded,
                      onPressed: _handleNewAnalysis,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildTagsSection(String title, List<String> tags, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.surface.withOpacity(0.5),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppColors.gold.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: AppColors.gold,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                title,
                style: GoogleFonts.inter(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.primaryText,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: tags.map((tag) => PowerChip(
              label: tag,
              selected: true,
            )).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildItemCard(DetectedItem item, int index) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.surface.withOpacity(0.5),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppColors.gold.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Item header with index
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: AppColors.gold.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  '#$index',
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: AppColors.gold,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item.name,
                      style: GoogleFonts.inter(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppColors.primaryText,
                      ),
                    ),
                    if (item.productType != null) ...[
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: AppColors.info.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          item.productType!,
                          style: GoogleFonts.inter(
                            fontSize: 11,
                            fontWeight: FontWeight.w600,
                            color: AppColors.info,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              if (item.price != null) ...[
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: AppColors.success.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    item.price!,
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      fontWeight: FontWeight.w700,
                      color: AppColors.success,
                    ),
                  ),
                ),
              ],
            ],
          ),

          const SizedBox(height: 16),

          // Item details
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (item.brand != null) ...[
                      _buildItemDetail('Brand', item.brand!, Icons.business_outlined),
                      const SizedBox(height: 8),
                    ],
                    if (item.productType != null) ...[
                      _buildItemDetail('Type', item.productType!, Icons.category_outlined),
                      const SizedBox(height: 8),
                    ],
                    if (item.size != null) ...[
                      _buildItemDetail('Size', item.size!, Icons.straighten_outlined),
                      const SizedBox(height: 8),
                    ],
                    if (item.description != null) ...[
                      _buildItemDetail('Description', item.description!, Icons.description_outlined),
                      const SizedBox(height: 8),
                    ],
                    _buildItemDetail(
                      'Confidence',
                      '${(item.confidence * 100).toStringAsFixed(1)}%',
                      Icons.verified_outlined,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildItemDetail(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: AppColors.gold,
        ),
        const SizedBox(width: 8),
        Text(
          '$label: ',
          style: GoogleFonts.inter(
            fontSize: 13,
            color: AppColors.secondaryText,
            fontWeight: FontWeight.w500,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: GoogleFonts.inter(
              fontSize: 13,
              color: AppColors.primaryText,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildResultItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: color, size: 20),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: GoogleFonts.inter(
                  fontSize: 14,
                  color: AppColors.secondaryText,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                value,
                style: GoogleFonts.inter(
                  fontSize: 16,
                  color: AppColors.primaryText,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _handleNewAnalysis() {
    Navigator.pushAndRemoveUntil(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const PhotoInputScreen(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(-1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.easeInOutCubic;

          var tween = Tween(
            begin: begin,
            end: end,
          ).chain(CurveTween(curve: curve));

          return SlideTransition(
            position: animation.drive(tween),
            child: FadeTransition(
              opacity: animation,
              child: child,
            ),
          );
        },
        transitionDuration: const Duration(milliseconds: 500),
      ),
      (route) => false,
    );
  }

  Widget _buildAnalyzingIndicator() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Animated analyzing indicator
          TweenAnimationBuilder<double>(
            tween: Tween(begin: 0.0, end: 1.0),
            duration: const Duration(seconds: 2),
            curve: Curves.easeInOut,
            builder: (context, value, child) {
              return Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: AppColors.gold.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(40),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.gold.withOpacity(value * 0.3),
                      blurRadius: 20,
                      spreadRadius: value * 5,
                    ),
                  ],
                ),
                child: Center(
                  child: CircularProgressIndicator(
                    strokeWidth: 3,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      AppColors.gold.withOpacity(0.8),
                    ),
                  ),
                ),
              );
            },
            onEnd: () {
              // Restart the animation
              Future.delayed(const Duration(milliseconds: 100), () {
                if (mounted && _isAnalyzing) {
                  setState(() {});
                }
              });
            },
          ),

          const SizedBox(height: 24),

          Text(
            'Analyzing Image...',
            style: GoogleFonts.inter(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.primaryText,
              letterSpacing: 0.1,
            ),
          ),

          const SizedBox(height: 8),

          Text(
            'Please wait while we process your image',
            style: GoogleFonts.inter(
              fontSize: 14,
              color: AppColors.secondaryText,
              letterSpacing: 0.1,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 700;

    return Scaffold(
      backgroundColor: AppColors.background,
      body: Container(
        decoration: BoxDecoration(
          gradient: RadialGradient(
            center: Alignment.bottomRight,
            radius: 1.2,
            colors: [AppColors.gold.withOpacity(0.02), AppColors.background],
          ),
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Column(
              children: [
                // Enhanced App Bar
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 16,
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: AppColors.gold.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          Icons.analytics_outlined,
                          color: AppColors.gold,
                          size: 28,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Analysis Results',
                              style: GoogleFonts.inter(
                                fontSize: 24,
                                fontWeight: FontWeight.w700,
                                color: AppColors.primaryText,
                                letterSpacing: -0.5,
                              ),
                            ),
                            Text(
                              _isAnalyzing
                                  ? 'Processing...'
                                  : 'Analysis complete',
                              style: GoogleFonts.inter(
                                fontSize: 14,
                                color: _isAnalyzing
                                    ? AppColors.gold
                                    : AppColors.success,
                                letterSpacing: 0.1,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                // Main content
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: isShortScreen ? 12 : 20,
                    ),
                    child: Column(
                      children: [
                        // Enhanced image preview
                        Container(
                          height: isShortScreen ? 200 : 280,
                          child: AnimatedBuilder(
                            animation: _imageController,
                            builder: (context, child) {
                              return Transform.scale(
                                scale: _imageScaleAnimation.value,
                                child: Hero(
                                  tag: 'selected_image',
                                  child: Container(
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(20),
                                      boxShadow: [
                                        BoxShadow(
                                          color: AppColors.gold.withOpacity(
                                            0.15,
                                          ),
                                          blurRadius: 25,
                                          spreadRadius: 5,
                                          offset: const Offset(0, 8),
                                        ),
                                        BoxShadow(
                                          color: Colors.black.withOpacity(0.3),
                                          blurRadius: 15,
                                          offset: const Offset(0, 4),
                                        ),
                                      ],
                                    ),
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(20),
                                      child: Container(
                                        decoration: BoxDecoration(
                                          border: Border.all(
                                            color: AppColors.gold.withOpacity(
                                              0.3,
                                            ),
                                            width: 2,
                                          ),
                                          borderRadius: BorderRadius.circular(
                                            20,
                                          ),
                                        ),
                                        child: ClipRRect(
                                          borderRadius: BorderRadius.circular(
                                            18,
                                          ),
                                          child: kIsWeb
                                              ? Image.memory(
                                                  widget.imageFile,
                                                  fit: BoxFit.cover,
                                                  width: double.infinity,
                                                )
                                              : Image.file(
                                                  widget.imageFile,
                                                  fit: BoxFit.cover,
                                                  width: double.infinity,
                                                ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ),

                        SizedBox(height: isShortScreen ? 20 : 32),

                        // Results or analyzing indicator
                        Expanded(
                          child: _isAnalyzing
                              ? _buildAnalyzingIndicator()
                              : _buildAnalysisResults(),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
