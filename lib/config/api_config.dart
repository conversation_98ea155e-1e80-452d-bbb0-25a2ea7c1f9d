/// Configuration for API endpoints and settings
class ApiConfig {
  // Hugging Face Inference Providers API Configuration (New Router-based API)
  static const String huggingFaceRouterUrl = 'https://router.huggingface.co/hf-inference/models';

  // Vision-Language Models available through the new API
  static const List<String> visionLanguageModels = [
    'meta-llama/Llama-3.2-11B-Vision-Instruct',
    'meta-llama/Llama-3.2-90B-Vision-Instruct',
    'microsoft/Phi-3.5-vision-instruct',
  ];

  // Fallback: Legacy Inference API (may still work for some models)
  static const String legacyInferenceUrl = 'https://api-inference.huggingface.co/models';
  static const List<String> legacyVisionModels = [
    'Salesforce/blip-image-captioning-large',
    'Salesforce/blip-image-captioning-base',
    'microsoft/git-large-coco',
    'nlpconnect/vit-gpt2-image-captioning',
  ];
  
  // Hugging Face API Token
  static const String huggingFaceToken = '*************************************';

  // API timeout settings (reduced with authenticated access)
  static const Duration apiTimeout = Duration(seconds: 45);
  static const Duration connectionTimeout = Duration(seconds: 20);
  
  // Default prompts for different analysis types
  static const Map<String, String> defaultPrompts = {
    'general': 'Analyze this image in detail. Describe what you see, including objects, colors, composition, and any notable features.',
    'objects': 'Identify and list all the objects visible in this image.',
    'colors': 'Describe the color palette and color scheme of this image.',
    'composition': 'Analyze the composition, lighting, and artistic elements of this image.',
    'text': 'Extract and transcribe any text visible in this image.',
  };
  
  // Model parameters
  static const Map<String, dynamic> defaultModelParams = {
    'temperature': 0.7,
    'max_tokens': 1000,
    'top_p': 0.9,
  };
  
  // Feature flags
  static const bool enableFallbackToMockData = true;
  static const bool enableRetryOnFailure = true;
  static const int maxRetryAttempts = 3;
}
